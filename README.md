# 彩云游戏自动化模拟器

基于两组手动操作的请求数据分析创建的Python脚本，用于模拟彩云游戏的自动化操作。

## 文件说明

- `caiyun_game_simulator.py` - 主要的模拟器脚本
- `config.py` - 配置文件，包含从原始请求中提取的token和参数
- `requests.js` - 原始的JavaScript请求数据（两组手动操作记录）
- `README.md` - 使用说明

## 功能特点

### 两种模拟方法

**第一种方法流程：**
1. 获取Token（登录认证）
2. 开始准备（获取时间戳）
3. 再次登录确认
4. 记录游戏点击行为
5. 完成游戏准备
6. 完成游戏任务

**第二种方法流程：**
1. 查询特殊Token
2. 开始准备（获取时间戳）
3. 执行登录认证
4. 完成准备
5. 完成游戏任务

### 关键差异分析

通过对比两组请求数据，发现了以下关键差异：

1. **认证方式不同**：
   - 第一组：直接使用 `tyrzLogin.action` 进行认证
   - 第二组：先调用 `querySpecToken` 获取特殊token，再进行认证

2. **ssoToken参数不同**：
   - 第一组：`YZsidssolg21a35821fbb5a45e7faaa41ff97eddb1`
   - 第二组：`YZsidssolgac3438a3c10173fda106ec7b4f445c45`

3. **动态参数**：
   - `jwtToken`：JWT认证令牌（会话相关）
   - `token`：加密的访问令牌
   - `x-nonce`：随机数（防重放攻击）
   - `x-request-id`：请求唯一标识
   - `x-signature`：请求签名（安全验证）
   - `x-timestamp`：时间戳

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 基本使用

```bash
python caiyun_game_simulator.py
```

### 交互式选择

运行脚本后，会提示选择：

1. **Token模式选择**：
   - `y`（默认）：使用从原始请求中提取的真实token数据
   - `n`：使用模拟生成的token数据

2. **模拟方法选择**：
   - `1`：运行第一种方法
   - `2`：运行第二种方法  
   - `3`：两种方法都尝试

### 编程方式使用

```python
from caiyun_game_simulator import CaiyunGameSimulator

# 使用真实token数据
simulator = CaiyunGameSimulator(use_real_tokens=True)

# 运行第一种方法
simulator.run_simulation_method1()

# 或运行第二种方法
simulator.run_simulation_method2()
```

## 配置说明

### config.py 配置项

- `FIRST_GROUP_TOKENS`：第一组请求的token信息
- `SECOND_GROUP_TOKENS`：第二组请求的token信息
- `FIRST_GAME_FINISH_TOKENS`：第一组完成游戏时的token
- `SECOND_GAME_FINISH_TOKENS`：第二组完成游戏时的token
- `CONFIG`：通用配置（URL、延迟、超时等）
- `COOKIES`：会话Cookie信息

### 可调整参数

```python
CONFIG = {
    "base_url": "https://caiyun.feixin.10086.cn:7071",
    "request_delay": 1,  # 请求间隔（秒）
    "timeout": 30,       # 请求超时时间（秒）
}
```

## 注意事项

### 安全提醒

1. **Token时效性**：从原始请求提取的token可能已过期，需要获取新的有效token
2. **账户安全**：请确保在安全的环境中使用，避免token泄露
3. **请求频率**：避免过于频繁的请求，可能触发反爬虫机制

### 技术限制

1. **签名算法**：当前使用简化的签名生成，实际可能需要更复杂的算法
2. **动态参数**：某些参数可能需要从服务器响应中动态获取
3. **会话管理**：可能需要更完善的会话状态管理

### 错误处理

脚本包含基本的错误处理，会输出请求状态和响应信息：

```
获取时间戳: 200
响应: {"success":true,"data":1754123456789}
```

## 开发说明

### 扩展功能

可以基于现有框架扩展更多功能：

1. **添加新的API端点**
2. **实现更复杂的业务逻辑**
3. **添加数据持久化**
4. **实现定时任务**

### 调试模式

可以在代码中添加更详细的日志输出：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 免责声明

本脚本仅用于学习和研究目的，请遵守相关服务的使用条款和法律法规。使用者需要自行承担使用风险。

## 更新日志

- v1.0：初始版本，实现基本的两种模拟方法
- 支持真实token和模拟token两种模式
- 包含完整的配置管理和错误处理
