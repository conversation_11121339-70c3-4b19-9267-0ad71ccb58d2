#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 存储从原始请求中提取的token和参数
"""

# 第一组请求的token信息
FIRST_GROUP_TOKENS = {
    "jwt_token": "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uoFAhrQSxk4KoYgDsTAcj23Aih3QqcHO_apsurGKbeY",
    "token": "wnhasXZT0QuvHacg1kiK3s32NG+uETvZtIPzq86ju5QHKB9Vz9quoJCRQtFtGe3U/wUfhh0Od0QnkP1WVTfXd5iUEdYK51YT6QlRgddrTZ6AbsOgSLV+fFj0Utoegdk4d/XCr5ssPH+LLnBoNwStxUfxYB8Ww/civIOXkUXBmXE=",
    "sso_token": "YZsidssolg21a35821fbb5a45e7faaa41ff97eddb1",
    "x_nonce": "b4469628-a454-4297-8514-5ad1b8e80393",
    "x_request_id": "52f39f95-b8c5-4414-8899-cbfa1736f3d5",
    "x_signature": "66913e1847202c7070800082d75c89e8",
    "x_timestamp": "*************"
}

# 第二组请求的token信息
SECOND_GROUP_TOKENS = {
    "jwt_token": "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vCQ7CNM6wY4gC6p5gCpMlmGFAZKayGfSjgABF09l7Y8",
    "token": "c8JeoR/EmhTjnsG5ek2aZEhga8kNGAcq3IGyCC1ErEpE6eXJrmPOg9o7Mzq031TTScHMrXlEaSF7dUzSML9Tc2Uom1AzXNa3Dh/nXIh3PFUvmxl6ib4oUQbsaRIvywGsv0iztpfj53cj48bOowsjAe7uS/FUtxj6QMOdOxfH1PI=",
    "sso_token": "YZsidssolgac3438a3c10173fda106ec7b4f445c45",
    "x_nonce": "e05bad6d-9baa-493c-b89d-da4b10a9e875",
    "x_request_id": "50f0576b-ab8a-4a56-830e-dea8d91aa7f7",
    "x_signature": "465a0ce850c861c16490d67b82070468",
    "x_timestamp": "*************"
}

# 游戏完成时的token信息（第一组）
FIRST_GAME_FINISH_TOKENS = {
    "jwt_token": "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6LL_ZVHyCAFQUthi28inEGl-M5q8vSmx35UXHpSYXTQ",
    "token": "eaVNWRbpcvjvViNoQ6+bijFqj4Zguqmuy3dhsIFITB7KdB+34ddwVcUAWdZKgHRJIh3Gs+FSklyvSJI57qyPl49PSdAuJdKey5aEPZNC5IOQFEZ2/kN7ywvMHgi7Qkxk19ybUpCU1WxPb5XJwqyfjF4p5rpchv2KTabZRDIusmE=",
    "x_nonce": "1fdf4ab7-b2fe-4a29-9e95-f56417348fd7",
    "x_request_id": "07fc0e11-2744-493c-8ebf-73c2f40439e5",
    "x_signature": "228f3d04686a6f1dbfcf14df210acd0f",
    "x_timestamp": "*************"
}

# 游戏完成时的token信息（第二组）
SECOND_GAME_FINISH_TOKENS = {
    "jwt_token": "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.g9TRZtuqoo8x4ltUFY7BLyC25DVe497Rlf-JXUe420c",
    "token": "XLgWPjgLKn9x3JXIqZz4TZZPqSn7+Vp1NZisn/LdlFPKkfU2AHQfP9l9I20L7LhbFUFyzJ2JywmJg5bA7VgcSgNIaA2d/dX9FbW5s0eQUJWOqfFudYyVhBT5eKFf9QPXhEzHvNR43xH+*******************************=",
    "x_nonce": "4797de40-67f2-4c56-a31f-6b79c326a04a",
    "x_request_id": "d35ef971-3dc9-42d2-a021-2b3b34fb3786",
    "x_signature": "fa8ad6e0bb9db8731049ed6e06fe305b",
    "x_timestamp": "*************"
}

# 通用配置
CONFIG = {
    "base_url": "https://caiyun.feixin.10086.cn:7071",
    "referer": "https://caiyun.feixin.10086.cn:7071/portal/synthesisonet/index.html?sourceid=1005",
    "user_agent": "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36 EdgA/138.0.2739.107",
    "request_delay": 1,  # 请求间隔（秒）
    "timeout": 30,  # 请求超时时间（秒）
}

# Cookie配置
COOKIES = {
    "bc_mo": "MTM1MjA2NTMyMjQ=",
    "bc_ps": "",
    "gdp_user_id": "gioenc-94758d70%2C1a4b%2C58a1%2Cc319%2C4147072e72c3",
    "9e4e5fa7244c6b6e_gdp_cs1": "",
    "9e4e5fa7244c6b6e_gdp_user_key": "",
    "9e4e5fa7244c6b6e_gdp_session_id_sent": "f6c701d4-7ae5-4ee1-9b6d-bba7a09aef25",
    "9e4e5fa7244c6b6e_gdp_sequence_ids": '{"globalKey":7,"VISIT":3,"PAGE":3,"CUSTOM":3}',
    "WT_FPC": "id=2b90225ccbca8a4946a1724290264953:lv=1742653388699:ss=1742653368745",
    "sensorsdata2015jssdkcross": '%7B%22distinct_id%22%3A%22CIDC-U-7c184fdb075a41738e663768ed49b0af%22%2C%22first_id%22%3A%22193514a8a833e3-0c3cf660c8062e-4c657b58-1327104-193514a8a8476f%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%2C%22phoneNumber%22%3A%22MTUxODI3NjkwMzQ%3D%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkzNTE0YThhODMzZTMtMGMzY2Y2NjBjODA2MmUtNGM2NTdiNTgtMTMyNzEwNC0xOTM1MTRhOGE4NDc2ZiIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IkNJREMtVS03YzE4NGZkYjA3NWE0MTczOGU2NjM3NjhlZDQ5YjBhZiJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22CIDC-U-7c184fdb075a41738e663768ed49b0af%22%7D%2C%22%24device_id%22%3A%2219584f64df3786-0c90305208499a8-4c657b58-1327104-19584f64df4ac8%22%7D',
    "SESSION": "ZWYwOGZjZWMtMWE0MC00NzFlLTkzNWItMjU5YzJlNWFjZjg1",
    "userDomainId": "1166700642574575911"
}
