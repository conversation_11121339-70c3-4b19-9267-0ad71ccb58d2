#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩云游戏自动化脚本
基于两组手动操作的请求数据分析创建
"""

import requests
import time
import uuid
import hashlib
import json
import random
from urllib.parse import urlencode
from config import (
    FIRST_GROUP_TOKENS, SECOND_GROUP_TOKENS,
    FIRST_GAME_FINISH_TOKENS, SECOND_GAME_FINISH_TOKENS,
    CONFIG, COOKIES
)


class CaiyunGameSimulator:
    def __init__(self, use_real_tokens=True):
        self.session = requests.Session()
        self.base_url = CONFIG["base_url"]

        # 基础headers
        self.base_headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?1",
            "sec-ch-ua-platform": '"Android"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "Referer": CONFIG["referer"],
            "User-Agent": CONFIG["user_agent"]
        }

        # 设置session的cookies
        self.session.cookies.update(COOKIES)

        # 设置超时
        self.session.timeout = CONFIG["timeout"]

        # 初始化token
        self.jwt_token = None
        self.token = None
        self.sso_token = None
        self.use_real_tokens = use_real_tokens
        
        # 从请求数据中提取的固定cookie值
        self.base_cookies = {
            "bc_mo": "MTM1MjA2NTMyMjQ=",
            "bc_ps": "",
            "gdp_user_id": "gioenc-94758d70%2C1a4b%2C58a1%2Cc319%2C4147072e72c3",
            "9e4e5fa7244c6b6e_gdp_cs1": "",
            "9e4e5fa7244c6b6e_gdp_user_key": "",
            "9e4e5fa7244c6b6e_gdp_session_id_sent": "f6c701d4-7ae5-4ee1-9b6d-bba7a09aef25",
            "9e4e5fa7244c6b6e_gdp_sequence_ids": '{"globalKey":7,"VISIT":3,"PAGE":3,"CUSTOM":3}',
            "WT_FPC": "id=2b90225ccbca8a4946a1724290264953:lv=1742653388699:ss=1742653368745",
            "sensorsdata2015jssdkcross": '%7B%22distinct_id%22%3A%22CIDC-U-7c184fdb075a41738e663768ed49b0af%22%2C%22first_id%22%3A%22193514a8a833e3-0c3cf660c8062e-4c657b58-1327104-193514a8a8476f%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%2C%22phoneNumber%22%3A%22MTUxODI3NjkwMzQ%3D%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkzNTE0YThhODMzZTMtMGMzY2Y2NjBjODA2MmUtNGM2NTdiNTgtMTMyNzEwNC0xOTM1MTRhOGE4NDc2ZiIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IkNJREMtVS03YzE4NGZkYjA3NWE0MTczOGU2NjM3NjhlZDQ5YjBhZiJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22CIDC-U-7c184fdb075a41738e663768ed49b0af%22%7D%2C%22%24device_id%22%3A%2219584f64df3786-0c90305208499a8-4c657b58-1327104-19584f64df4ac8%22%7D',
            "SESSION": "ZWYwOGZjZWMtMWE0MC00NzFlLTkzNWItMjU5YzJlNWFjZjg1",
            "userDomainId": "1166700642574575911"
        }
        
        # 动态参数（需要从实际响应中获取或生成）
        self.jwt_token = None
        self.token = None
        self.sso_token = None
        
    def generate_uuid(self):
        """生成UUID"""
        return str(uuid.uuid4())
    
    def generate_timestamp(self):
        """生成时间戳"""
        return str(int(time.time() * 1000))
    
    def generate_signature(self, timestamp, nonce, token=""):
        """生成签名（这里是模拟，实际算法可能更复杂）"""
        # 这是一个简化的签名生成，实际可能需要更复杂的算法
        data = f"{timestamp}{nonce}{token}"
        return hashlib.md5(data.encode()).hexdigest()
    
    def get_current_time_millis(self):
        """获取当前时间毫秒数"""
        url = f"{self.base_url}/portal/ajax/tools/opRequest.action"
        headers = self.base_headers.copy()
        headers.update({
            "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
            "priority": "u=0, i",
            "x-requested-with": "XMLHttpRequest"
        })
        
        data = "op=currentTimeMillis"
        
        try:
            response = self.session.post(url, headers=headers, data=data)
            print(f"获取时间戳: {response.status_code}")
            if response.status_code == 200:
                print(f"响应: {response.text}")
            return response
        except Exception as e:
            print(f"获取时间戳失败: {e}")
            return None
    
    def login_with_sso_token(self, sso_token):
        """使用SSO Token登录"""
        url = f"{self.base_url}/portal/auth/v2/tyrzLogin.action"
        params = {
            "ssoToken": sso_token,
            "openAccount": "0"
        }
        
        # 生成动态参数
        timestamp = self.generate_timestamp()
        nonce = self.generate_uuid()
        request_id = self.generate_uuid()
        
        headers = self.base_headers.copy()
        headers.update({
            "priority": "u=1, i",
            "x-nonce": nonce,
            "x-request-id": request_id,
            "x-timestamp": timestamp
        })
        
        # 如果有jwt_token和token，添加到headers
        if self.jwt_token:
            headers["jwttoken"] = self.jwt_token
        if self.token:
            headers["token"] = self.token
            headers["x-signature"] = self.generate_signature(timestamp, nonce, self.token)
        
        try:
            response = self.session.get(url, headers=headers, params=params)
            print(f"SSO登录: {response.status_code}")
            if response.status_code == 200:
                print(f"响应: {response.text}")
                # 尝试从响应中提取token信息
                self._extract_tokens_from_response(response)
            return response
        except Exception as e:
            print(f"SSO登录失败: {e}")
            return None
    
    def query_spec_token(self):
        """查询特殊Token"""
        url = f"{self.base_url}/ycloud/api/cloud/userdomain/v2/querySpecToken"
        params = {"targetSourceId": "001005"}
        
        timestamp = self.generate_timestamp()
        nonce = self.generate_uuid()
        request_id = self.generate_uuid()
        
        headers = self.base_headers.copy()
        headers.update({
            "priority": "u=1, i",
            "showloading": "true",
            "x-nonce": nonce,
            "x-request-id": request_id,
            "x-timestamp": timestamp
        })
        
        if self.jwt_token:
            headers["jwttoken"] = self.jwt_token
        if self.token:
            headers["token"] = self.token
            headers["x-signature"] = self.generate_signature(timestamp, nonce, self.token)
        
        try:
            response = self.session.get(url, headers=headers, params=params)
            print(f"查询特殊Token: {response.status_code}")
            if response.status_code == 200:
                print(f"响应: {response.text}")
                self._extract_tokens_from_response(response)
            return response
        except Exception as e:
            print(f"查询特殊Token失败: {e}")
            return None
    
    def record_game_tap(self):
        """记录游戏点击"""
        url = f"{self.base_url}/portal/journaling"
        
        headers = self.base_headers.copy()
        headers.update({
            "accept": "application/json, text/javascript, */*; q=0.01",
            "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
            "priority": "u=0, i",
            "x-requested-with": "XMLHttpRequest"
        })
        
        if self.jwt_token:
            headers["jwttoken"] = self.jwt_token
        
        data = "module=uservisit&optkeyword=synthesisonet_game_tap&fromId=&flag=&fileId=&fileType=&fileExtname=&fileSize=&sourceid=1005&linkId="
        
        try:
            response = self.session.post(url, headers=headers, data=data)
            print(f"记录游戏点击: {response.status_code}")
            if response.status_code == 200:
                print(f"响应: {response.text}")
            return response
        except Exception as e:
            print(f"记录游戏点击失败: {e}")
            return None
    
    def finish_game(self):
        """完成游戏"""
        url = f"{self.base_url}/market/signin/hecheng1T/finish"
        params = {"flag": "true"}
        
        timestamp = self.generate_timestamp()
        nonce = self.generate_uuid()
        request_id = self.generate_uuid()
        
        headers = self.base_headers.copy()
        headers.update({
            "priority": "u=1, i",
            "x-nonce": nonce,
            "x-request-id": request_id,
            "x-timestamp": timestamp
        })
        
        if self.jwt_token:
            headers["jwttoken"] = self.jwt_token
        if self.token:
            headers["token"] = self.token
            headers["x-signature"] = self.generate_signature(timestamp, nonce, self.token)
        
        try:
            response = self.session.get(url, headers=headers, params=params)
            print(f"完成游戏: {response.status_code}")
            if response.status_code == 200:
                print(f"响应: {response.text}")
            return response
        except Exception as e:
            print(f"完成游戏失败: {e}")
            return None
    
    def _extract_tokens_from_response(self, response):
        """从响应中提取token信息"""
        try:
            # 尝试从响应头或响应体中提取token信息
            # 这里需要根据实际API响应格式来实现
            if 'Set-Cookie' in response.headers:
                cookies = response.headers['Set-Cookie']
                # 解析cookie中的token信息
                pass
            
            # 尝试解析JSON响应
            if response.headers.get('content-type', '').startswith('application/json'):
                data = response.json()
                if 'token' in data:
                    self.token = data['token']
                if 'jwtToken' in data:
                    self.jwt_token = data['jwtToken']
        except Exception as e:
            print(f"提取token失败: {e}")
    
    def run_simulation_method1(self):
        """运行第一种方法的模拟"""
        print("=== 开始第一种方法模拟 ===")

        if self.use_real_tokens:
            # 使用真实的token数据
            self.jwt_token = FIRST_GROUP_TOKENS["jwt_token"]
            self.token = FIRST_GROUP_TOKENS["token"]
            self.sso_token = FIRST_GROUP_TOKENS["sso_token"]
            print("使用真实token数据")
        else:
            # 使用第一组数据中的ssoToken
            self.sso_token = "YZsidssolg21a35821fbb5a45e7faaa41ff97eddb1"
            print("使用模拟token数据")

        # 1. 获取Token（登录）
        print("1. 执行登录...")
        self.login_with_sso_token(self.sso_token)
        time.sleep(CONFIG["request_delay"])

        # 2. 开始准备
        print("2. 获取时间戳...")
        self.get_current_time_millis()
        time.sleep(CONFIG["request_delay"])

        # 3. 再次登录
        print("3. 再次登录...")
        self.login_with_sso_token(self.sso_token)
        time.sleep(CONFIG["request_delay"])

        # 4. 游戏中点击
        print("4. 记录游戏点击...")
        self.record_game_tap()
        time.sleep(CONFIG["request_delay"])

        # 5. 完成游戏准备
        print("5. 完成游戏准备...")
        self.get_current_time_millis()
        time.sleep(CONFIG["request_delay"])

        # 6. 完成游戏 - 使用完成游戏的token
        if self.use_real_tokens:
            self.jwt_token = FIRST_GAME_FINISH_TOKENS["jwt_token"]
            self.token = FIRST_GAME_FINISH_TOKENS["token"]
        print("6. 完成游戏...")
        self.finish_game()

        print("=== 第一种方法模拟完成 ===")
    
    def run_simulation_method2(self):
        """运行第二种方法的模拟"""
        print("=== 开始第二种方法模拟 ===")

        if self.use_real_tokens:
            # 使用真实的token数据
            self.jwt_token = SECOND_GROUP_TOKENS["jwt_token"]
            self.token = SECOND_GROUP_TOKENS["token"]
            self.sso_token = SECOND_GROUP_TOKENS["sso_token"]
            print("使用真实token数据")
        else:
            # 使用第二组数据中的ssoToken
            self.sso_token = "YZsidssolgac3438a3c10173fda106ec7b4f445c45"
            print("使用模拟token数据")

        # 1. 获取特殊Token
        print("1. 查询特殊Token...")
        self.query_spec_token()
        time.sleep(CONFIG["request_delay"])

        # 2. 开始准备
        print("2. 获取时间戳...")
        self.get_current_time_millis()
        time.sleep(CONFIG["request_delay"])

        # 3. 登录
        print("3. 执行登录...")
        self.login_with_sso_token(self.sso_token)
        time.sleep(CONFIG["request_delay"])

        # 4. 完成准备
        print("4. 完成准备...")
        self.get_current_time_millis()
        time.sleep(CONFIG["request_delay"])

        # 5. 完成游戏 - 使用完成游戏的token
        if self.use_real_tokens:
            self.jwt_token = SECOND_GAME_FINISH_TOKENS["jwt_token"]
            self.token = SECOND_GAME_FINISH_TOKENS["token"]
        print("5. 完成游戏...")
        self.finish_game()

        print("=== 第二种方法模拟完成 ===")


def main():
    """主函数"""
    print("彩云游戏自动化模拟器")
    print("基于两组手动操作请求数据分析")
    print("-" * 50)

    # 选择是否使用真实token
    use_real = input("是否使用真实token数据? (y/n, 默认y): ").lower()
    use_real_tokens = use_real != 'n'

    simulator = CaiyunGameSimulator(use_real_tokens=use_real_tokens)

    print(f"Token模式: {'真实数据' if use_real_tokens else '模拟数据'}")
    print("-" * 50)

    choice = input("选择模拟方法 (1: 第一种方法, 2: 第二种方法, 3: 两种都试): ")

    if choice == "1":
        simulator.run_simulation_method1()
    elif choice == "2":
        simulator.run_simulation_method2()
    elif choice == "3":
        print("先尝试第一种方法:")
        simulator.run_simulation_method1()
        print("\n" + "="*50 + "\n")
        print("再尝试第二种方法:")
        # 创建新的模拟器实例以避免token混淆
        simulator2 = CaiyunGameSimulator(use_real_tokens=use_real_tokens)
        simulator2.run_simulation_method2()
    else:
        print("无效选择，默认运行第一种方法")
        simulator.run_simulation_method1()


if __name__ == "__main__":
    main()
