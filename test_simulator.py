#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 用于验证彩云游戏模拟器的功能
"""

import sys
import time
from caiyun_game_simulator import CaiyunGameSimulator
from config import FIRST_GROUP_TOKENS, SECOND_GROUP_TOKENS

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    # 测试模拟器初始化
    simulator = CaiyunGameSimulator(use_real_tokens=True)
    print("✓ 模拟器初始化成功")
    
    # 测试UUID生成
    uuid1 = simulator.generate_uuid()
    uuid2 = simulator.generate_uuid()
    assert uuid1 != uuid2, "UUID生成失败"
    print("✓ UUID生成功能正常")
    
    # 测试时间戳生成
    timestamp1 = simulator.generate_timestamp()
    time.sleep(0.001)
    timestamp2 = simulator.generate_timestamp()
    assert int(timestamp1) < int(timestamp2), "时间戳生成失败"
    print("✓ 时间戳生成功能正常")
    
    # 测试签名生成
    signature = simulator.generate_signature("1234567890", "test-nonce", "test-token")
    assert len(signature) == 32, "签名长度不正确"
    print("✓ 签名生成功能正常")
    
    print("基本功能测试完成\n")

def test_token_configuration():
    """测试token配置"""
    print("=== 测试Token配置 ===")
    
    # 检查第一组token
    required_keys = ["jwt_token", "token", "sso_token", "x_nonce", "x_request_id", "x_signature", "x_timestamp"]
    for key in required_keys:
        assert key in FIRST_GROUP_TOKENS, f"第一组缺少{key}"
        assert FIRST_GROUP_TOKENS[key], f"第一组{key}为空"
    print("✓ 第一组token配置完整")
    
    # 检查第二组token
    for key in required_keys:
        assert key in SECOND_GROUP_TOKENS, f"第二组缺少{key}"
        assert SECOND_GROUP_TOKENS[key], f"第二组{key}为空"
    print("✓ 第二组token配置完整")
    
    # 验证token格式
    assert FIRST_GROUP_TOKENS["jwt_token"].startswith("eyJ"), "第一组JWT token格式错误"
    assert SECOND_GROUP_TOKENS["jwt_token"].startswith("eyJ"), "第二组JWT token格式错误"
    print("✓ JWT token格式正确")
    
    print("Token配置测试完成\n")

def test_request_methods():
    """测试请求方法（不实际发送请求）"""
    print("=== 测试请求方法 ===")
    
    simulator = CaiyunGameSimulator(use_real_tokens=False)
    
    # 设置测试token
    simulator.jwt_token = "test_jwt_token"
    simulator.token = "test_token"
    simulator.sso_token = "test_sso_token"
    
    # 测试方法是否可调用（不实际发送请求）
    try:
        # 这些方法会尝试发送请求，但我们只测试方法是否存在和可调用
        assert hasattr(simulator, 'get_current_time_millis'), "缺少get_current_time_millis方法"
        assert hasattr(simulator, 'login_with_sso_token'), "缺少login_with_sso_token方法"
        assert hasattr(simulator, 'query_spec_token'), "缺少query_spec_token方法"
        assert hasattr(simulator, 'record_game_tap'), "缺少record_game_tap方法"
        assert hasattr(simulator, 'finish_game'), "缺少finish_game方法"
        print("✓ 所有请求方法存在")
        
        assert hasattr(simulator, 'run_simulation_method1'), "缺少run_simulation_method1方法"
        assert hasattr(simulator, 'run_simulation_method2'), "缺少run_simulation_method2方法"
        print("✓ 所有模拟方法存在")
        
    except Exception as e:
        print(f"✗ 方法测试失败: {e}")
        return False
    
    print("请求方法测试完成\n")
    return True

def test_headers_generation():
    """测试请求头生成"""
    print("=== 测试请求头生成 ===")
    
    simulator = CaiyunGameSimulator(use_real_tokens=True)
    
    # 测试基础headers
    assert "accept" in simulator.base_headers, "缺少accept头"
    assert "User-Agent" in simulator.base_headers, "缺少User-Agent头"
    assert "Referer" in simulator.base_headers, "缺少Referer头"
    print("✓ 基础请求头配置正确")
    
    # 测试动态headers生成
    timestamp = simulator.generate_timestamp()
    nonce = simulator.generate_uuid()
    request_id = simulator.generate_uuid()
    
    assert len(timestamp) >= 13, "时间戳长度不正确"
    assert len(nonce) == 36, "nonce长度不正确"  # UUID格式
    assert len(request_id) == 36, "request_id长度不正确"
    print("✓ 动态请求头参数生成正确")
    
    print("请求头生成测试完成\n")

def run_dry_test():
    """运行干跑测试（不发送实际请求）"""
    print("=== 干跑测试 ===")
    
    simulator = CaiyunGameSimulator(use_real_tokens=True)
    
    # 设置真实token
    simulator.jwt_token = FIRST_GROUP_TOKENS["jwt_token"]
    simulator.token = FIRST_GROUP_TOKENS["token"]
    simulator.sso_token = FIRST_GROUP_TOKENS["sso_token"]
    
    print("模拟器配置:")
    print(f"  JWT Token: {simulator.jwt_token[:50]}...")
    print(f"  Token: {simulator.token[:50]}...")
    print(f"  SSO Token: {simulator.sso_token}")
    print(f"  Base URL: {simulator.base_url}")
    
    print("✓ 干跑测试完成")
    print("注意：实际请求需要有效的网络连接和有效的token\n")

def main():
    """主测试函数"""
    print("彩云游戏模拟器测试套件")
    print("=" * 50)
    
    try:
        # 运行所有测试
        test_basic_functionality()
        test_token_configuration()
        test_request_methods()
        test_headers_generation()
        run_dry_test()
        
        print("=" * 50)
        print("✓ 所有测试通过！")
        print("\n可以运行以下命令开始实际模拟:")
        print("python caiyun_game_simulator.py")
        
    except AssertionError as e:
        print(f"✗ 测试失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
