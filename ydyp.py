'''
移动云盘
功能：自动签到，使用签到奖品，支持多账号（使用#或&分割token），支持青龙

更新：23.07
cron： 1 1 1 * * *
by：cherwin
'''

import os
import json
import time
import requests
import xml.etree.ElementTree as ET
from os import environ, path
import uuid
import hashlib

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(requests.packages.urllib3.exceptions.InsecureRequestWarning)

def load_send():
    global send
    cur_path = path.abspath(path.dirname(__file__))
    if path.exists(cur_path + "/notify.py"):
        try:
            from notify import send
            print("加载通知服务成功！")
        except:
            send = False
            print("加载通知服务失败~")
    else:
        send = False
        print("加载通知服务失败~")

load_send()

class CaiyunCloud:
    def __init__(self, ck):
        self.ck = ck
        self.account = ''
        self.authorization = ''
        self.session = ''
        self.user_token = ''
        self.jwt_token = ''
        self.phone = ''
        self.content_id = ''
        self.task_id = ''
        self.current_time = str(int(time.time() * 1000))
        self.inviter_count = 0
        self.invitation_counts = {}

        # 解析账号信息
        for part in ck.split(';'):
            if 'account' in part:
                self.account = part.split('=')[1].strip()
            elif 'Authorization' in part:
                self.authorization = part.split('=')[1].strip()

        # 基础headers
        self.headers = {
            'Host': "caiyun.feixin.10086.cn:7071",
            'Accept': "*/*",
            'Accept-Language': "zh-CN,zh-Hans;q=0.9",
            'Accept-Encoding': "gzip, deflate, br",
            'Connection': "keep-alive",
            'User-Agent': "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MCloudApp/11.1.1 iPhone AppLanguage/zh-CN",
            'Sec-Fetch-Site': "same-origin",
            'Sec-Fetch-Mode': "cors",
            'Sec-Fetch-Dest': "empty",
            'X-Requested-With': "XMLHttpRequest"
        }

        # 文件操作headers
        self.file_headers = {
            'Host': 'orches.yun.139.com',
            'x-yun-tid': '3a79e6fe-c6e8-4b23-a597-87c6f6',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.31(0x18001f37) NetType/WIFI Language/zh_CN',
            'Referer': 'https://servicewechat.com/wx4e4ed37286c816c2/106/page-frame.html',
            'content-type': 'application/json',
        }

    def request(self, method, url, headers=None, data=None, json_data=None, verify=False):
        try:
            # 合并基础headers和传入的headers
            final_headers = {**self.headers, **(headers or {})}

            # 添加时间戳和请求ID
            final_headers['x-timestamp'] = str(int(time.time() * 1000))
            final_headers['x-request-id'] = str(uuid.uuid4())

            # 如果URL包含特定路径，添加额外的headers
            if 'hecheng1T' in url:
                final_headers['x-signature'] = hashlib.md5(f"{final_headers['x-timestamp']}{final_headers['x-request-id']}".encode()).hexdigest()
                final_headers['x-nonce'] = str(uuid.uuid4())

            print(f"\n请求信息:")
            print(f"方法: {method}")
            print(f"URL: {url}")
            print(f"Headers: {json.dumps(final_headers, indent=2, ensure_ascii=False)}")
            if data:
                print(f"Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
            if json_data:
                print(f"JSON Data: {json.dumps(json_data, indent=2, ensure_ascii=False)}")

            resp = requests.request(method, url, headers=final_headers, data=data, json=json_data, verify=verify)

            print(f"\n响应信息:")
            print(f"状态码: {resp.status_code}")
            print(f"响应头: {json.dumps(dict(resp.headers), indent=2, ensure_ascii=False)}")
            try:
                resp_json = resp.json()
                print(f"响应内容: {json.dumps(resp_json, indent=2, ensure_ascii=False)}")
                return resp_json if resp.status_code == 200 else None
            except json.JSONDecodeError:
                print(f"响应内容(非JSON): {resp.text}")
                return None
        except Exception as e:
            print(f"请求异常: {e}")
            return None

    def get_session(self):
        url = "https://aas.caiyun.feixin.10086.cn:443/tellin/querySpecToken.do"
        headers = {
            'Host': "aas.caiyun.feixin.10086.cn:443",
            'Authorization': f"Basic {self.authorization}",
            'Content-Type': "text/xml;charset=UTF-8",
        }
        body = f"<?xml version='1.0' encoding='UTF-8' standalone='no' ?><root><account>{self.account}</account><toSourceId>001005</toSourceId></root>"

        try:
            response = requests.post(url, headers=headers, data=body.encode("utf-8"), verify=True)
            print(f"\n获取Session响应:")
            print(f"状态码: {response.status_code}")
            print(f"响应头: {json.dumps(dict(response.headers), indent=2, ensure_ascii=False)}")
            print(f"响应内容: {response.text}")

            if response.status_code == 200:
                root = ET.fromstring(response.text)
                self.session = root.findtext("token")
                print(f"获取到的Session: {self.session}")
                return bool(self.session)
        except Exception as e:
            print(f"获取session失败: {e}")
        return False

    def get_user_token(self):
        if not self.session:
            print("Session为空，无法获取user_token")
            return False

        url = f"https://caiyun.feixin.10086.cn:7071/portal/auth/tyrzLogin.action?ssoToken={self.session}"
        headers = {
            "Referer": f"https://example.com/your/endpoint?ssoToken={self.session}",
            'X-Requested-With': 'XMLHttpRequest'
        }

        try:
            resp = self.request('GET', url, headers=headers, verify=True)
            if resp and resp.get('code') == 0:
                result = resp.get('result', {})
                self.user_token = result.get("account")
                self.jwt_token = result.get("token")
                self.headers['token'] = self.user_token
                self.headers['jwtToken'] = self.jwt_token
                print(f"获取到的User Token: {self.user_token}")
                print(f"获取到的JWT Token: {self.jwt_token}")
                return True
            else:
                print('token失效了，准备发送通知')
                send("移动云盘", f"手机号：{self.account}")
        except Exception as e:
            print(f"获取user_token失败: {e}")
        return False

    def game_info(self):
        url = "https://caiyun.feixin.10086.cn:7071/market/signin/hecheng1T/info?op=info"
        result = self.request('GET', url, headers=self.headers)
        if not result or result.get("code") != 0:
            print("获取游戏信息失败")
            return False

        info = result["result"].get("info", {})
        history = result["result"].get("history", {})
        self.phone = info.get("phone")
        count_info = history.get("0", {})

        self.count = int(count_info.get("count", 0))
        self.rank = count_info.get("rank", "0")
        self.curr = int(info.get("curr", 0))
        self.invite = int(info.get("invite", 0))
        self.exchange = int(info.get("exchange", 0))

        print(f"手机号：{self.phone}")
        print(f"已累计合成：{self.count}次🏆   当前排名：第{self.rank}名🏅   剩余游戏次数：{self.curr}次🎯")

        if self.exchange >= 5:
            print("可兑换次数足够，进行游戏次数兑换🏀")
            self.exchange_game()
            self.curr += 2
            time.sleep(2)
        else:
            print("已兑换过，跳过兑换⛔")

        if self.curr >= 5:
            print("进行邀请获取游戏次数🚩")
            for other_account in accounts:
                if other_account != self.ck:
                    self.inviter(other_account)
                    time.sleep(2)
                    self.inviter_count += 1
        else:
            print("无多余次数，跳过邀请🔕")

        self.new_curr = self.curr - self.inviter_count
        print(f"还剩余游戏次数：{self.new_curr}次🎈")

        if self.new_curr == 0:
            print("游戏次数已经用完，跳过游戏⚠️")
        else:
            print("游戏次数充足，准备开始游戏🎾")
            for _ in range(self.new_curr):
                self.start_game()
                time.sleep(2)
                self.finish_game()
                time.sleep(2)
        return True

    def inviter(self, other_account):
        target_account = ""
        for part in other_account.split(';'):
            if 'account' in part:
                target_account = part.split('=')[1].strip()
                break

        if self.account == target_account:
            print("不会邀请自己")
            return

        invitation_count = self.invitation_counts.get(target_account, 0)
        if invitation_count >= 3:
            print(f"{self.account}已经邀请了{target_account} 3次，无需再次邀请")
            return

        url = f'https://caiyun.feixin.10086.cn:7071/market/signin/hecheng1T/beinvite?inviter={target_account}'
        try:
            resp = self.request('GET', url, headers=self.headers)
            if resp and resp.get('code') == 0:
                print(f"邀请{target_account}进行游戏👫")
                self.finish_game()
                time.sleep(2)
                self.invitation_counts[target_account] = invitation_count + 1
            else:
                print(f"{target_account}的游戏次数已用完")
        except Exception as e:
            print(f"邀请失败: {str(e)}")

    def start_game(self):
        url = 'https://caiyun.feixin.10086.cn/market/signin/hecheng1T/beinvite'
        resp = self.request('GET', url, headers=self.headers)
        if resp and resp.get('code') == 0:
            print("开始游戏🎮")
        else:
            print("游戏次数已用完")

    def finish_game(self):
        url = 'https://caiyun.feixin.10086.cn/market/signin/hecheng1T/finish?flag=true'
        resp = self.request('GET', url, headers=self.headers)
        if resp and resp.get('code') == 0:
            print("合成1T泡泡成功👏")
        else:
            print("合成失败，可能token已失效！")

    def exchange_game(self):
        url = 'https://caiyun.feixin.10086.cn:7071/market/signin/hecheng1T/exchange?num=5'
        resp = self.request('GET', url, headers=self.headers)
        if resp and resp.get('code') == 0:
            print("游戏次数兑换成功🎉")
        else:
            print("兑换游戏失败，可能token已失效！")

    def wx_sign(self):
        url = "https://caiyun.feixin.10086.cn:7071/market/playoffic/followSignInfo?isWx=true"
        headers = {**self.headers, "Referer": "https://caiyun.feixin.10086.cn:7071/portal/caiyunOfficialAccount/index.html?path=attentionCourtesy&sourceid=1001"}
        resp = self.request('GET', url, headers=headers)
        if resp and resp.get("code") == 0:
            print("公众号签到成功")

    def draw_info(self):
        url = "https://caiyun.feixin.10086.cn:7071/market/playoffic/drawInfo"
        headers = {**self.headers, "Referer": "https://caiyun.feixin.10086.cn:7071/portal/caiyunOfficialAccount/index.html?path=attentionCourtesy&sourceid=1001"}
        resp = self.request('GET', url, headers=headers)
        if resp and resp.get("code") == 0:
            result = resp.get("result", {})
            print(f"手机号：{self.account}")
            print(f"当前云朵：{result.get('surplusPoints', 0)}☁️")
            if result.get("surplusNumber") == 50:
                self.draw()
            else:
                print("首次免费已使用，跳过抽奖")
        else:
            print("获取账号信息失败")

    def draw(self):
        url = "https://caiyun.feixin.10086.cn:7071/market/playoffic/draw"
        headers = {**self.headers, "Referer": "https://caiyun.feixin.10086.cn:7071/portal/caiyunOfficialAccount/index.html?path=attentionCourtesy&sourceid=1001"}
        resp = self.request('GET', url, headers=headers)
        if resp and resp.get("code") == 0:
            print(f"获得奖品：{resp['result']['prizeName']}🎁")

    def app_sign(self):
        url = "https://caiyun.feixin.10086.cn:7071/market/signin/page/info?client=app"
        headers = {**self.headers, "Origin": "https://caiyun.feixin.10086.cn:7071", "Referer": f"https://caiyun.feixin.10086.cn:7071/portal/newsignin/index.html?sourceid=1097&enableShare=1&ssoToken={self.session}"}
        resp = self.request('GET', url, headers=headers)
        if resp and resp.get("code") == 0:
            print("APP签到成功")
        else:
            print(resp)

    def shake(self):
        url = "https://caiyun.feixin.10086.cn:7071/market/shake-server/shake/shakeIt?flag=1"
        headers = {**self.headers, "Origin": "https://caiyun.feixin.10086.cn:7071", "token": self.user_token, "Referer": f"https://caiyun.feixin.10086.cn:7071/portal/caiyunOfficialAccount/index.html?path=shakeActivity&sourceid=1011&token={self.session}=1"}
        resp = self.request('POST', url, headers=headers)
        if resp and resp.get("code") == 0:
            result = resp.get("result", {})
            if result.get("shakePrizeconfig", {}).get("name"):
                print(f"获得奖品：{result['shakePrizeconfig']['name']}🎁")
            else:
                print("未中奖")

    def click_upload(self):
        url = "https://caiyun.feixin.10086.cn:7071/market/signin/task/click?key=task&id=106"
        headers = {**self.headers, "Referer": f"https://caiyun.feixin.10086.cn:7071/portal/newsignin/index.html?sourceid=1097&enableShare=1&ssoToken={self.session}"}
        resp = self.request('GET', url, headers=headers)
        if resp and resp.get("code") == 0:
            print("开始执行上传")
            time.sleep(2)
            self.upload_file()

    def upload_file(self):
        url = "https://orches.yun.139.com/orchestration/personalCloud/uploadAndDownload/v1.0/pcUploadFileRequest"
        headers = {**self.file_headers, 'Authorization': f"Basic {self.authorization}"}
        data = {
            "commonAccountInfo": {"account": self.account},
            "fileCount": 1,
            "totalSize": 5059,
            "uploadContentList": [{
                "contentName": "恭喜发财.docx",
                "contentSize": 5059,
                "comlexFlag": 0,
                "digest": "48e0c0379eff55686b1320c2f3dd3067"
            }],
            "newCatalogName": "",
            "parentCatalogID": "00019700101000000001",
            "operation": 0,
            "path": "",
            "manualRename": 2,
            "autoCreatePath": [],
            "tagID": "",
            "tagType": "",
            "seqNo": ""
        }

        resp = self.request('POST', url, headers=headers, json_data=data)
        if resp and resp.get("data", {}).get("uploadResult", {}).get("newContentIDList", [{}])[0].get("contentID"):
            self.content_id = resp["data"]["uploadResult"]["newContentIDList"][0]["contentID"]
            print("文件上传成功📔")
            time.sleep(2)
            self.delete_task()
        else:
            print("文件上传失败")

    def delete_task(self):
        url = "https://orches.yun.139.com/orchestration/personalCloud/batchOprTask/v1.0/createBatchOprTask"
        headers = {**self.file_headers, 'Authorization': f"Basic {self.authorization}"}
        data = {
            "createBatchOprTaskReq": {
                "commonAccountInfo": {"account": self.account},
                "taskType": 2,
                "moduleType": 3,
                "actionType": 201,
                "taskInfo": {
                    "catalogInfoList": [],
                    "contentInfoList": [self.content_id]
                }
            }
        }

        resp = self.request('POST', url, headers=headers, json_data=data)
        if resp and resp.get("data", {}).get("createBatchOprTaskRes", {}).get("taskID"):
            self.task_id = resp["data"]["createBatchOprTaskRes"]["taskID"]
            print("创建删除文件任务")
            time.sleep(2)
            self.delete_file()
        else:
            print("创建删除任务失败")

    def delete_file(self):
        url = "https://orches.yun.139.com/orchestration/personalCloud/batchOprTask/v1.0/queryBatchOprTaskDetail"
        headers = {**self.file_headers, 'Authorization': f"Basic {self.authorization}"}
        data = {
            "queryBatchOprTaskDetailReq": {
                "commonAccountInfo": {"account": self.account},
                "taskID": self.task_id
            }
        }

        resp = self.request('POST', url, headers=headers, json_data=data)
        if resp:
            print("文件删除成功🗑")
            time.sleep(2)
            self.get_gift()
        else:
            print("文件删除失败")

    def click_note(self):
        url = "https://caiyun.feixin.10086.cn:7071/market/signin/task/click?key=task&id=107"
        headers = {**self.headers, "Referer": f"https://caiyun.feixin.10086.cn:7071/portal/newsignin/index.html?sourceid=1097&enableShare=1&ssoToken={self.session}"}
        resp = self.request('GET', url, headers=headers)
        if resp and resp.get("code") == 0:
            print("开始执行笔记任务")
            time.sleep(2)
            self.get_notebookid()

    def get_notebookid(self):
        url = "https://mnote.caiyun.feixin.10086.cn/noteServer/api/syncNotebookV3.do"
        headers = {
            'APP_NUMBER': self.account,
            'APP_AUTH': f"Basic {self.authorization}",
            'CP_VERSION': '3.2.0',
            'NOTE_TOKEN': 'djF8dDF8N0Q1RDk4NDg4Qjk1QzM2N0M0NzU3ODQ4NUEzQ0E8N0RBNkM5OTZCRkU4RjYyNEMwRjhF',
            'APP_CP': 'iphone',
            'Host': 'mnote.caiyun.feixin.10086.cn',
        }
        data = {
            "delNotebooks": [],
            "notebookRefs": [],
            "addNotebooks": [],
            "updateNotebooks": []
        }

        resp = self.request('POST', url, headers=headers, json_data=data)
        if resp and resp.get("notebooks", []):
            self.notebook_id = resp["notebooks"][0].get("notebookId")
            print("笔记上传成功✏️")
            time.sleep(2)
            self.create_note()
        else:
            print("获取笔记本ID失败")

    def create_note(self):
        url = "https://mnote.caiyun.feixin.10086.cn/noteServer/api/createNote.do"
        headers = {
            'APP_AUTH': f"Basic {self.authorization}",
            'APP_NUMBER': self.account,
            'X-Tingyun-Id': 'p35OnrDoP8k;c=2;r=*********;u=d8207fec292cd11385b89eecb5f78682::3E893447B0BA78D6',
            'NOTE_TOKEN': 'djF8dDF8QUEwODVGNjlGOUYxQkD8OvFz2T05g-5TVM6m3WnMElEb9eg0vLJazDCA62lsmPfSE05NzWgBxV_D8jDU6t1g2D7nJz-Wu2e9-SMFd1AC6iNLSE8RlM1O9iLgR7fYEDy6fqSt1yBBi5dPfB0SipKJvCWTXetnFgCpHSOxtTlVx-xKkcWj4_X_6.W6qtJd',
            'User-Agent': 'mobile',
            'APP_CP': 'iphone',
            'Cookie': 'JSESSIONID=886D741FE39899DE2D5D9C57B04B839D',
            'Host': 'mnote.caiyun.feixin.10086.cn',
        }
        body = {
            "location": "",
            "contentid": "",
            "title": "恭喜发财",
            "visitTime": "",
            "tags": [{
                "id": self.current_time,
                "orderIndex": "0",
                "userphone": self.account,
                "text": "默认笔记本"
            }],
            "updateTime": self.current_time,
            "revision": "1",
            "archived": 1,
            "noteId": "BDAC5B2D868F4D75B02AD85E2B61CFF9",
            "remindtype": 0,
            "remindtime": "",
            "contents": [{
                "noteId": "BDAC5B2D868F4D75B02AD85E2B61CFF9",
                "data": "<font color=\"#14263f\">恭喜发财<\/font>",
                "type": "TEXT",
                "sortOrder": "0"
            }],
            "system": "mobile",
            "isEdited": 0,
            "version": "1.00",
            "attachmentdirid": "",
            "attachments": [],
            "notestatus": 0,
            "userphone": self.account,
            "createTime": self.current_time,
            "topmost": "0",
            "attachmentdir": "BDAC5B2D868F4D75B02AD85E2B61CFF9",
            "latlng": "",
            "description": "iphone"
        }

        resp = self.request('POST', url, headers=headers, json_data=body)
        if resp:
            print("笔记创建成功✏️")
            time.sleep(2)
            self.get_gift()
        else:
            print("笔记创建失败")

    def get_gift(self):
        url = "https://caiyun.feixin.10086.cn:7071/market/signin/page/receive"
        headers = {**self.headers, "Referer": f"https://caiyun.feixin.10086.cn:7071/portal/newsignin/index.html?sourceid=1097&enableShare=1&ssoToken={self.session}"}
        resp = self.request('GET', url, headers=headers)
        if resp and resp.get("code") == 0:
            print("每日任务奖励领取成功♥️")

    def main(self):
        try:
            self.draw_info()
            time.sleep(2)
            self.wx_sign()
            time.sleep(2)
            self.app_sign()
            time.sleep(2)
            self.click_upload()
            time.sleep(2)
            self.game_info()
            time.sleep(2)

            for _ in range(50):
                self.shake()
                time.sleep(2)
        except Exception as e:
            print(f"执行出错: {e}")

if __name__ == "__main__":
    caiyun_ck = environ.get("CAIYUN_COOKIE", "")
    if not caiyun_ck:
        print("未填写 CAIYUN_COOKIE 变量")
        exit(0)

    accounts = caiyun_ck.replace('&', '#').split('#')
    print(f"\n>>>>>>>>>>共获取到{len(accounts)}个账号<<<<<<<<<<")

    for indx, ck in enumerate(accounts):
        print(f"\n----------准备执行第{indx + 1}个账号----------")
        cloud = CaiyunCloud(ck)
        if cloud.get_session() and cloud.get_user_token():
            cloud.main()
